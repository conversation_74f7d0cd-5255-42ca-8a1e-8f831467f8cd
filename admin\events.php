<?php
// Start session
session_start();

// Check if the user is authenticated
if (!isset($_SESSION['auth_user']) || empty($_SESSION['auth_user']['id'])) {
    // User is not logged in, redirect to login page
    header('Location: login.php');
    exit; // Make sure to exit to prevent further script execution
}

// Include database connection
require_once 'database/conn.php';

// Create a simple database wrapper class for this file
class SimpleDB {
    private $pdo;

    public function __construct($pdo) {
        $this->pdo = $pdo;
    }

    public function fetchAll($sql, $params = []) {
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }

    public function fetchRow($sql, $params = []) {
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetch();
    }

    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));

        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($data);

        return $this->pdo->lastInsertId();
    }

    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->pdo->prepare($sql);
        return $stmt->execute($params);
    }

    public function update($table, $data, $where, $params = []) {
        $set = implode(', ', array_map(function($key) {
            return "{$key} = :{$key}";
        }, array_keys($data)));

        $sql = "UPDATE {$table} SET {$set} WHERE {$where}";
        $stmt = $this->pdo->prepare($sql);

        // Merge data and where params
        $allParams = array_merge($data, $params);
        return $stmt->execute($allParams);
    }
}

// Create database instance
$db = new SimpleDB($pdo);

// Initialize variables
$success_message = '';
$error_message = '';
$form_data = [];

// Handle form submission for creating new event
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !isset($_POST['delete_event'])) {
    try {
        // Validate and sanitize input data
        $category = trim(htmlspecialchars($_POST['category'] ?? '', ENT_QUOTES, 'UTF-8'));
        $title = trim(htmlspecialchars($_POST['title'] ?? '', ENT_QUOTES, 'UTF-8'));
        $location = trim(htmlspecialchars($_POST['location'] ?? '', ENT_QUOTES, 'UTF-8'));
        $price = floatval($_POST['price'] ?? 0);
        $seats = intval($_POST['seats'] ?? 0);
        $event_date = $_POST['event_date'] ?? '';
        $event_starting_time = $_POST['event_starting_time'] ?? '';
        $event_ending_time = $_POST['event_ending_time'] ?? '';
        $host = trim(htmlspecialchars($_POST['host'] ?? '', ENT_QUOTES, 'UTF-8'));
        $user_id = $_SESSION['auth_user']['id'];

        // Store form data for repopulation on error
        $form_data = [
            'category' => $category,
            'title' => $title,
            'location' => $location,
            'price' => $price,
            'seats' => $seats,
            'event_date' => $event_date,
            'event_starting_time' => $event_starting_time,
            'event_ending_time' => $event_ending_time,
            'host' => $host
        ];

        // Validation
        $errors = [];

        if (empty($category)) $errors[] = "Category is required";
        if (empty($title)) $errors[] = "Title is required";
        if (empty($location)) $errors[] = "Location is required";
        if ($price < 0) $errors[] = "Price must be a positive number";
        if ($seats <= 0) $errors[] = "Seats must be a positive number";
        if (empty($event_date)) $errors[] = "Event date is required";
        if (empty($event_starting_time)) $errors[] = "Event starting time is required";
        if (empty($event_ending_time)) $errors[] = "Event ending time is required";
        if (empty($host)) $errors[] = "Host is required";

        // Validate date format
        if (!empty($event_date)) {
            $date = DateTime::createFromFormat('Y-m-d', $event_date);
            if (!$date || $date->format('Y-m-d') !== $event_date) {
                $errors[] = "Invalid date format";
            } elseif ($date < new DateTime('today')) {
                $errors[] = "Event date cannot be in the past";
            }
        }

        // Validate time format and logic
        if (!empty($event_starting_time) && !empty($event_ending_time)) {
            if ($event_starting_time >= $event_ending_time) {
                $errors[] = "Event ending time must be after starting time";
            }
        }

        // Handle file upload - Store only file path in database
        $image_path = null;
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = 'uploads/events/';

            // Create upload directory if it doesn't exist
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }

            $file_extension = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

            // Validate file type
            if (!in_array($file_extension, $allowed_extensions)) {
                $errors[] = "Only JPG, JPEG, PNG, GIF, and WebP files are allowed";
            }
            // Validate file size (5MB limit)
            elseif ($_FILES['image']['size'] > 5 * 1024 * 1024) {
                $errors[] = "File size must be less than 5MB";
            }
            // Validate actual image file (security check)
            elseif (!getimagesize($_FILES['image']['tmp_name'])) {
                $errors[] = "Invalid image file";
            }
            else {
                // Generate unique filename to prevent conflicts
                $unique_filename = uniqid('event_', true) . '.' . $file_extension;
                $upload_path = $upload_dir . $unique_filename;

                // Move uploaded file to destination
                if (move_uploaded_file($_FILES['image']['tmp_name'], $upload_path)) {
                    // Store only the relative path in database
                    $image_path = $upload_path;
                } else {
                    $errors[] = "Failed to upload event image";
                }
            }
        } else {
            $errors[] = "Event image is required";
        }

        // If no errors, insert event
        if (empty($errors)) {
            $event_data = [
                'category' => $category,
                'title' => $title,
                'location' => $location,
                'price' => $price,
                'seats' => $seats,
                'image' => $image_path,
                'event_date' => $event_date,
                'event_starting_time' => $event_starting_time,
                'event_ending_time' => $event_ending_time,
                'host' => $host,
                'user_id' => $user_id
            ];

            try {
                $event_id = $db->insert('upcoming_events', $event_data);

                if ($event_id) {
                    $_SESSION['success_message'] = "Event created successfully! Event ID: " . $event_id;
                    // Redirect to prevent resubmission
                    header('Location: events.php');
                    exit;
                } else {
                    $error_message = "Failed to create event. Please try again.";
                }
            } catch (Exception $e) {
                error_log("Event insertion error: " . $e->getMessage());
                if (strpos($e->getMessage(), "upcoming_events") !== false && strpos($e->getMessage(), "doesn't exist") !== false) {
                    $error_message = "Events table doesn't exist. Please refresh the page to create it automatically.";
                } else {
                    $error_message = "Database error: " . $e->getMessage();
                }
            }
        } else {
            $error_message = implode('<br>', $errors);
        }

    } catch (Exception $e) {
        error_log("Event creation error: " . $e->getMessage());
        $error_message = "An error occurred while creating the event. Please try again.";
    }
}

// Handle delete action
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    try {
        $event_id = (int)$_GET['id'];

        // Get event image path before deleting
        $event = $db->fetchRow("SELECT image FROM upcoming_events WHERE id = ?", [$event_id]);

        if ($event && $db->delete('upcoming_events', 'id = ?', [$event_id])) {
            // Delete the image file if it exists
            if ($event['image'] && file_exists($event['image'])) {
                unlink($event['image']);
            }
            $_SESSION['success_message'] = "Event deleted successfully!";
        } else {
            $_SESSION['error_message'] = "Failed to delete event.";
        }
    } catch (Exception $e) {
        error_log("Event deletion error: " . $e->getMessage());
        $_SESSION['error_message'] = "An error occurred while deleting the event.";
    }

    // Redirect to prevent resubmission
    header('Location: events.php');
    exit;
}

// Handle delete form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_event'])) {
    try {
        $event_id = (int)$_POST['event_id'];

        // Get event image path before deleting
        $event = $db->fetchRow("SELECT image FROM upcoming_events WHERE id = ?", [$event_id]);

        if ($event && $db->delete('upcoming_events', 'id = ?', [$event_id])) {
            // Delete the image file if it exists
            if ($event['image'] && file_exists($event['image'])) {
                unlink($event['image']);
            }
            $_SESSION['success_message'] = "Event deleted successfully!";
        } else {
            $_SESSION['error_message'] = "Failed to delete event.";
        }
    } catch (Exception $e) {
        error_log("Event deletion error: " . $e->getMessage());
        $_SESSION['error_message'] = "An error occurred while deleting the event.";
    }

    // Redirect to prevent resubmission
    header('Location: events.php');
    exit;
}

// Check if upcoming_events table exists and create it if it doesn't
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'upcoming_events'");
    $table_exists = $stmt->rowCount() > 0;

    if (!$table_exists) {
        // Create the table
        $create_table_sql = "
        CREATE TABLE IF NOT EXISTS `upcoming_events` (
          `id` int(11) NOT NULL AUTO_INCREMENT,
          `category` varchar(200) NOT NULL,
          `title` varchar(255) NOT NULL,
          `location` varchar(255) NOT NULL,
          `price` decimal(25,2) NOT NULL DEFAULT 0.00,
          `seats` int(11) NOT NULL,
          `image` varchar(255) NOT NULL,
          `event_date` date NOT NULL,
          `event_starting_time` time NOT NULL,
          `event_ending_time` time NOT NULL,
          `host` varchar(255) NOT NULL,
          `user_id` int(11) NOT NULL,
          `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
          `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          PRIMARY KEY (`id`),
          KEY `user_id` (`user_id`),
          KEY `event_date` (`event_date`),
          KEY `category` (`category`),
          CONSTRAINT `upcoming_events_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci";

        $pdo->exec($create_table_sql);
        $_SESSION['success_message'] = "Events table created successfully. You can now start creating events.";
    }
} catch (Exception $e) {
    error_log("Error checking/creating events table: " . $e->getMessage());
    $error_message = "Database table error. Please contact administrator.";
}

// Fetch all events for display
try {
    $events = $db->fetchAll("SELECT * FROM upcoming_events ORDER BY event_date ASC, event_starting_time ASC");
} catch (Exception $e) {
    $events = [];
    error_log("Error fetching events: " . $e->getMessage());
    if (strpos($e->getMessage(), "doesn't exist") !== false) {
        $error_message = "Events table doesn't exist. Please contact administrator to set up the database.";
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Event Management | Admin Dashboard</title>
    <!-- Stylesheets -->
    <link rel="shortcut icon" href="./assets/images/favicon.ico" type="image/x-icon">
    <link href="./assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/fontawesome.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/brands.min.css" rel="stylesheet">
    <link href="./assets/icons/fontawesome/css/solid.min.css" rel="stylesheet">
    <link href="./assets/plugin/datatable/datatables.min.css" rel="stylesheet">
    <link href="./assets/css/style.css" rel="stylesheet">
    <style>
        .event-img {
            width: 80px;
            height: 50px;
            border-radius: 4px;
            object-fit: cover;
        }
        .btn-info {
            background-color: #17a2b8;
            border-color: #17a2b8;
            color: #fff;
        }
        .btn-info:hover {
            background-color: #138496;
            border-color: #117a8b;
            color: #fff;
        }
        .btn-primary {
            background-color: #2a1c19;
            border-color: #2a1c19;
        }
        .btn-primary:hover {
            background-color: #1a100d;
            border-color: #1a100d;
        }
        .dataTables_wrapper .dataTables_filter input {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 12px;
            margin-left: 5px;
        }
        .dataTables_wrapper .dataTables_length select {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 6px 12px;
            margin: 0 5px;
        }
        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background: #2a1c19;
            color: #fff !important;
            border: 1px solid #2a1c19;
        }
        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            background: #1a100d;
            color: #fff !important;
            border: 1px solid #1a100d;
        }
        .table-responsive {
            overflow-x: auto;
        }
        .price-cell {
            font-weight: bold;
            color: #28a745;
        }
        .seats-cell {
            font-weight: bold;
            color: #007bff;
        }
        .event-status {
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            font-weight: bold;
        }
        .status-upcoming {
            background-color: #d4edda;
            color: #155724;
        }
        .status-today {
            background-color: #fff3cd;
            color: #856404;
        }
        .status-past {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <!-- Preloader -->
    <div id="preloader">
        <div class="spinner"></div>
    </div>
    <!-- Main Wrapper -->
    <div id="main-wrapper" class="d-flex">
       <!--sidebar here -->
           <?php include 'include/sidebar.php'; ?>
       <!-- Content Wrapper -->
        <div class="content-wrapper">
            <!-- Header -->
            <div class="header d-flex align-items-center justify-content-between">
                <div class="d-flex align-items-center">
                    <div class="collapse-sidebar me-3 d-none d-lg-block text-color-1"><span><i class="fa-solid fa-bars font-size-24"></i></span></div>
                    <div class="menu-toggle me-3 d-block d-lg-none text-color-1"><span><i class="fa-solid fa-bars font-size-24"></i></span></div>
                    <div class="d-none d-md-block d-lg-block">
                        <div class="input-group flex-nowrap">
                            <span class="input-group-text bg-white " id="addon-wrapping"><i class="fa-solid search-icon fa-magnifying-glass text-color-1"></i></span>
                            <input type="text" class="form-control search-input border-l-none ps-0" placeholder="Search anything" aria-label="Username" aria-describedby="addon-wrapping">
                        </div>
                    </div>
                </div>
                <div class="d-flex align-items-center">
                    <ul class="nav d-flex align-items-center">
                         <!-- User Profile -->
                        <li class="nav-item dropdown user-profile">
                            <div class="d-flex align-items-center dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <span class="user-avatar me-0 me-lg-3">
                                    <?php
                                    // Get profile image path and create proper URL
                                    $profile_image_path = isset($_SESSION['auth_user']['profile_image']) ? $_SESSION['auth_user']['profile_image'] : '';
                                    $profile_image_url = '';

                                    if (!empty($profile_image_path)) {
                                        // Extract filename from path regardless of directory structure
                                        $filename = basename($profile_image_path);

                                        // Always use the uploads/profiles directory with the extracted filename
                                        $corrected_path = './uploads/profiles/' . $filename;

                                        // Check if file exists in corrected path
                                        if (file_exists($corrected_path)) {
                                            $profile_image_url = $corrected_path;
                                        }
                                        // If corrected path doesn't work, try the original path
                                        elseif (file_exists($profile_image_path)) {
                                            $profile_image_url = $profile_image_path;
                                        }
                                    }

                                    // Display image if we have a valid URL
                                    if (!empty($profile_image_url)): ?>
                                        <img src="<?php echo htmlspecialchars($profile_image_url); ?>" alt="User" class="rounded-circle" style="width: 40px; height: 40px; object-fit: cover;">
                                    <?php else: ?>
                                        <!-- Fallback to letter avatar with proper styling -->
                                        <div class="rounded-circle d-flex align-items-center justify-content-center text-white fw-bold"
                                             style="width: 40px; height: 40px; background-color: #6c757d; font-size: 16px;">
                                            <?php
                                            if (isset($_SESSION['auth_user']['name']) && !empty($_SESSION['auth_user']['name'])) {
                                                echo strtoupper(substr(htmlspecialchars($_SESSION['auth_user']['name']), 0, 1));
                                            } else {
                                                echo 'A'; // Default initial
                                            }
                                            ?>
                                        </div>
                                    <?php endif; ?>
                                </span>
                                <div>
                                    <a href="#" class="d-none d-lg-block">
                                        <span class="d-block auth-role">
                                            <?php
                                                if (isset($_SESSION['auth_user']['role'])) {
                                                    echo htmlspecialchars(ucfirst(str_replace('_', ' ', $_SESSION['auth_user']['role'])));
                                                } else {
                                                    echo 'Guest'; // Default role
                                                }
                                            ?>
                                        </span>
                                        <span class="auth-name">
                                            <?php echo isset($_SESSION['auth_user']['name']) ? htmlspecialchars($_SESSION['auth_user']['name']) : 'Guest User'; // Default name ?>
                                        </span>
                                        <span class="ms-2 text-color-1 text-size-sm"><i class="fa-solid fa-angle-down"></i></span>
                                    </a>
                                    <ul class="dropdown-menu mt-3">
                                        <li><a class="dropdown-item" href="#">Profile</a></li>
                                        <li><a class="dropdown-item" href="#">Settings</a></li>
                                        <li><hr class="dropdown-divider"></li>

                                    </ul>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Main Content -->
            <div class="main-content">
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex align-items-lg-center flex-column flex-md-row flex-lg-row mt-3">
                            <div class="flex-grow-1">
                                <h3 class="mb-2 text-color-2">Event Management</h3>
                                <p class="text-muted">Create and manage upcoming events</p>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if (isset($_SESSION['success_message'])): ?>
                    <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
                        <i class="fa-solid fa-check-circle me-2"></i>
                        <?php
                        echo $_SESSION['success_message'];
                        unset($_SESSION['success_message']);
                        ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['error_message'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                        <i class="fa-solid fa-exclamation-triangle me-2"></i>
                        <?php
                        echo $_SESSION['error_message'];
                        unset($_SESSION['error_message']);
                        ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
                        <i class="fa-solid fa-exclamation-triangle me-2"></i>
                        <?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endif; ?>

                <!-- Event Creation Form -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card shadow-sm border-0">
                            <div class="card-header bg-white py-3">
                                <h5 class="card-title mb-0 text-color-2">
                                    <i class="fas fa-plus-circle me-2"></i>Create New Event
                                </h5>
                            </div>
                            <div class="card-body">
                                <form action="" method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                                    <div class="row g-3">
                                        <!-- Category Field -->
                                        <div class="col-md-6">
                                            <label for="category" class="form-label text-color-2 fw-medium">Category <span class="text-danger">*</span></label>
                                            <select class="form-select" id="category" name="category" required>
                                                <option value="">Select Category</option>
                                                <option value="Conference" <?php echo (isset($form_data['category']) && $form_data['category'] === 'Conference') ? 'selected' : ''; ?>>Conference</option>
                                                <option value="Workshop" <?php echo (isset($form_data['category']) && $form_data['category'] === 'Workshop') ? 'selected' : ''; ?>>Workshop</option>
                                                <option value="Seminar" <?php echo (isset($form_data['category']) && $form_data['category'] === 'Seminar') ? 'selected' : ''; ?>>Seminar</option>
                                                <option value="Training" <?php echo (isset($form_data['category']) && $form_data['category'] === 'Training') ? 'selected' : ''; ?>>Training</option>
                                                <option value="Webinar" <?php echo (isset($form_data['category']) && $form_data['category'] === 'Webinar') ? 'selected' : ''; ?>>Webinar</option>
                                                <option value="Networking" <?php echo (isset($form_data['category']) && $form_data['category'] === 'Networking') ? 'selected' : ''; ?>>Networking</option>
                                                <option value="Other" <?php echo (isset($form_data['category']) && $form_data['category'] === 'Other') ? 'selected' : ''; ?>>Other</option>
                                            </select>
                                            <div class="invalid-feedback">Please select a category.</div>
                                        </div>

                                        <!-- Title Field -->
                                        <div class="col-md-6">
                                            <label for="title" class="form-label text-color-2 fw-medium">Event Title <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="title" name="title"
                                                   value="<?php echo htmlspecialchars($form_data['title'] ?? ''); ?>"
                                                   placeholder="Enter event title" required>
                                            <div class="invalid-feedback">Please provide an event title.</div>
                                        </div>

                                        <!-- Location Field -->
                                        <div class="col-md-6">
                                            <label for="location" class="form-label text-color-2 fw-medium">Location <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="location" name="location"
                                                   value="<?php echo htmlspecialchars($form_data['location'] ?? ''); ?>"
                                                   placeholder="Enter event location" required>
                                            <div class="invalid-feedback">Please provide the event location.</div>
                                        </div>

                                        <!-- Price Field -->
                                        <div class="col-md-6">
                                            <label for="price" class="form-label text-color-2 fw-medium">Price <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control" id="price" name="price"
                                                       value="<?php echo htmlspecialchars($form_data['price'] ?? ''); ?>"
                                                       placeholder="0.00" min="0" step="0.01" required>
                                                <div class="invalid-feedback">Please provide a valid price.</div>
                                            </div>
                                            <small class="text-muted">Enter 0 for free events</small>
                                        </div>

                                        <!-- Seats Field -->
                                        <div class="col-md-6">
                                            <label for="seats" class="form-label text-color-2 fw-medium">Available Seats <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="seats" name="seats"
                                                   value="<?php echo htmlspecialchars($form_data['seats'] ?? ''); ?>"
                                                   placeholder="Enter number of seats" min="1" required>
                                            <div class="invalid-feedback">Please provide the number of available seats.</div>
                                        </div>

                                        <!-- Event Date Field -->
                                        <div class="col-md-6">
                                            <label for="event_date" class="form-label text-color-2 fw-medium">Event Date <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" id="event_date" name="event_date"
                                                   value="<?php echo htmlspecialchars($form_data['event_date'] ?? ''); ?>"
                                                   min="<?php echo date('Y-m-d'); ?>" required>
                                            <div class="invalid-feedback">Please select the event date.</div>
                                        </div>

                                        <!-- Event Starting Time Field -->
                                        <div class="col-md-3">
                                            <label for="event_starting_time" class="form-label text-color-2 fw-medium">Start Time <span class="text-danger">*</span></label>
                                            <input type="time" class="form-control" id="event_starting_time" name="event_starting_time"
                                                   value="<?php echo htmlspecialchars($form_data['event_starting_time'] ?? ''); ?>" required>
                                            <div class="invalid-feedback">Please select the start time.</div>
                                        </div>

                                        <!-- Event Ending Time Field -->
                                        <div class="col-md-3">
                                            <label for="event_ending_time" class="form-label text-color-2 fw-medium">End Time <span class="text-danger">*</span></label>
                                            <input type="time" class="form-control" id="event_ending_time" name="event_ending_time"
                                                   value="<?php echo htmlspecialchars($form_data['event_ending_time'] ?? ''); ?>" required>
                                            <div class="invalid-feedback">Please select the end time.</div>
                                        </div>

                                        <!-- Host Field -->
                                        <div class="col-md-6">
                                            <label for="host" class="form-label text-color-2 fw-medium">Host/Organizer <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="host" name="host"
                                                   value="<?php echo htmlspecialchars($form_data['host'] ?? ''); ?>"
                                                   placeholder="Enter host or organizer name" required>
                                            <div class="invalid-feedback">Please provide the host/organizer name.</div>
                                        </div>

                                        <!-- Image Field -->
                                        <div class="col-md-6">
                                            <label for="image" class="form-label text-color-2 fw-medium">Event Image <span class="text-danger">*</span></label>
                                            <input type="file" class="form-control" id="image" name="image" accept="image/*" required>
                                            <div class="invalid-feedback">Please select an event image.</div>
                                            <small class="text-muted">
                                                <i class="fa-solid fa-info-circle me-1"></i>
                                                <strong>Supported formats:</strong> JPG, JPEG, PNG, GIF, WebP.
                                                <strong>Max size:</strong> 5MB.
                                            </small>
                                        </div>
                                    </div>

                                    <div class="row mt-4">
                                        <div class="col-12">
                                            <button type="submit" class="btn bg-primary text-white me-2">
                                                <i class="fas fa-save me-2"></i>Create Event
                                            </button>
                                            <button type="reset" class="btn btn-outline-secondary">
                                                <i class="fas fa-undo me-2"></i>Reset Form
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Events Table -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card shadow-sm border-0">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover align-middle" id="eventsTable">
                                        <thead class="bg-light">
                                            <tr>
                                                <th scope="col" class="py-3">ID</th>
                                                <th scope="col" class="py-3">Image</th>
                                                <th scope="col" class="py-3">Category</th>
                                                <th scope="col" class="py-3">Title</th>
                                                <th scope="col" class="py-3">Location</th>
                                                <th scope="col" class="py-3">Price</th>
                                                <th scope="col" class="py-3">Seats</th>
                                                <th scope="col" class="py-3">Date & Time</th>
                                                <th scope="col" class="py-3">Host</th>
                                                <th scope="col" class="py-3">Status</th>
                                                <th scope="col" class="py-3 text-center">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (!empty($events)): ?>
                                                <?php foreach ($events as $event): ?>
                                                    <?php
                                                    // Determine event status
                                                    $event_date = new DateTime($event['event_date']);
                                                    $today = new DateTime('today');
                                                    $status = '';
                                                    $status_class = '';

                                                    if ($event_date < $today) {
                                                        $status = 'Past';
                                                        $status_class = 'status-past';
                                                    } elseif ($event_date == $today) {
                                                        $status = 'Today';
                                                        $status_class = 'status-today';
                                                    } else {
                                                        $status = 'Upcoming';
                                                        $status_class = 'status-upcoming';
                                                    }
                                                    ?>
                                                    <tr data-event-id="<?php echo $event['id']; ?>">
                                                        <td>
                                                            <span class="fw-medium text-color-2">#<?php echo $event['id']; ?></span>
                                                        </td>
                                                        <td>
                                                            <?php if ($event['image'] && file_exists($event['image'])): ?>
                                                                <img src="<?php echo htmlspecialchars($event['image']); ?>"
                                                                     alt="Event Image"
                                                                     class="event-img"
                                                                     data-bs-toggle="tooltip"
                                                                     title="<?php echo htmlspecialchars($event['title']); ?>">
                                                            <?php else: ?>
                                                                <div class="bg-light rounded d-flex align-items-center justify-content-center event-img">
                                                                    <i class="fas fa-calendar text-muted"></i>
                                                                </div>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <span class="badge bg-secondary"><?php echo htmlspecialchars($event['category']); ?></span>
                                                        </td>
                                                        <td>
                                                            <span class="fw-medium text-color-2"><?php echo htmlspecialchars($event['title']); ?></span>
                                                        </td>
                                                        <td>
                                                            <i class="fas fa-map-marker-alt text-muted me-1"></i>
                                                            <?php echo htmlspecialchars($event['location']); ?>
                                                        </td>
                                                        <td>
                                                            <span class="price-cell">
                                                                <?php echo $event['price'] > 0 ? '$' . number_format($event['price'], 2) : 'Free'; ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <span class="seats-cell"><?php echo number_format($event['seats']); ?></span>
                                                        </td>
                                                        <td>
                                                            <div class="small">
                                                                <div><i class="fas fa-calendar text-muted me-1"></i><?php echo date('M d, Y', strtotime($event['event_date'])); ?></div>
                                                                <div><i class="fas fa-clock text-muted me-1"></i><?php echo date('g:i A', strtotime($event['event_starting_time'])) . ' - ' . date('g:i A', strtotime($event['event_ending_time'])); ?></div>
                                                            </div>
                                                        </td>
                                                        <td><?php echo htmlspecialchars($event['host']); ?></td>
                                                        <td>
                                                            <span class="event-status <?php echo $status_class; ?>"><?php echo $status; ?></span>
                                                        </td>
                                                        <td class="text-center">
                                                            <a href="#" data-bs-toggle="modal" data-bs-target="#viewEventModal"
                                                               data-id="<?php echo $event['id']; ?>"
                                                               data-category="<?php echo htmlspecialchars($event['category']); ?>"
                                                               data-title="<?php echo htmlspecialchars($event['title']); ?>"
                                                               data-location="<?php echo htmlspecialchars($event['location']); ?>"
                                                               data-price="<?php echo htmlspecialchars($event['price']); ?>"
                                                               data-seats="<?php echo htmlspecialchars($event['seats']); ?>"
                                                               data-image="<?php echo htmlspecialchars($event['image']); ?>"
                                                               data-event-date="<?php echo htmlspecialchars($event['event_date']); ?>"
                                                               data-start-time="<?php echo htmlspecialchars($event['event_starting_time']); ?>"
                                                               data-end-time="<?php echo htmlspecialchars($event['event_ending_time']); ?>"
                                                               data-host="<?php echo htmlspecialchars($event['host']); ?>"
                                                               data-created="<?php echo htmlspecialchars($event['created_at']); ?>"
                                                               data-updated="<?php echo htmlspecialchars($event['updated_at']); ?>"
                                                               class="btn btn-sm btn-info me-2 view-event">
                                                                <i class="fa-regular fa-eye"></i>
                                                            </a>
                                                            <a href="#" data-bs-toggle="modal" data-bs-target="#editEventModal"
                                                               data-id="<?php echo $event['id']; ?>"
                                                               data-category="<?php echo htmlspecialchars($event['category']); ?>"
                                                               data-title="<?php echo htmlspecialchars($event['title']); ?>"
                                                               data-location="<?php echo htmlspecialchars($event['location']); ?>"
                                                               data-price="<?php echo htmlspecialchars($event['price']); ?>"
                                                               data-seats="<?php echo htmlspecialchars($event['seats']); ?>"
                                                               data-image="<?php echo htmlspecialchars($event['image']); ?>"
                                                               data-event-date="<?php echo htmlspecialchars($event['event_date']); ?>"
                                                               data-start-time="<?php echo htmlspecialchars($event['event_starting_time']); ?>"
                                                               data-end-time="<?php echo htmlspecialchars($event['event_ending_time']); ?>"
                                                               data-host="<?php echo htmlspecialchars($event['host']); ?>"
                                                               class="btn btn-sm btn-primary me-2 edit-event">
                                                                <i class="fa-regular fa-pen-to-square"></i>
                                                            </a>
                                                            <a href="#" data-bs-toggle="modal" data-bs-target="#deleteEventModal"
                                                               data-event-id="<?php echo $event['id']; ?>"
                                                               data-event-title="<?php echo htmlspecialchars($event['title']); ?>"
                                                               class="btn btn-sm btn-danger delete-event">
                                                                <i class="fa-solid fa-trash-can"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php else: ?>
                                                <tr>
                                                    <td colspan="11" class="text-center py-4">
                                                        <div class="text-muted">
                                                            <i class="fa-solid fa-calendar-days fa-2x mb-3"></i>
                                                            <p>No events found in the system.</p>
                                                            <p class="small">Create your first event using the form above.</p>
                                                            <?php if (isset($error_message) && !empty($error_message)): ?>
                                                                <div class="alert alert-warning mt-3">
                                                                    <i class="fa-solid fa-exclamation-triangle me-2"></i>
                                                                    <?php echo $error_message; ?>
                                                                </div>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- View Event Modal -->
                <div class="modal fade" id="viewEventModal" tabindex="-1" aria-labelledby="viewEventModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="viewEventModalLabel">Event Details</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <img id="viewEventImage" src="" class="img-fluid rounded" alt="Event Image">
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Category:</label>
                                            <p><span id="viewEventCategory" class="badge bg-secondary"></span></p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Title:</label>
                                            <p id="viewEventTitle"></p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Location:</label>
                                            <p id="viewEventLocation"></p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Price:</label>
                                            <p id="viewEventPrice" class="price-cell"></p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Available Seats:</label>
                                            <p id="viewEventSeats" class="seats-cell"></p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Date & Time:</label>
                                            <p id="viewEventDateTime"></p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Host:</label>
                                            <p id="viewEventHost"></p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Created:</label>
                                            <p id="viewEventCreated"></p>
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label fw-bold">Last Updated:</label>
                                            <p id="viewEventUpdated"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Edit Event Modal -->
                <div class="modal fade" id="editEventModal" tabindex="-1" aria-labelledby="editEventModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="editEventModalLabel">Edit Event</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <form id="editEventForm" method="post" enctype="multipart/form-data">
                                    <input type="hidden" name="event_id" id="edit_event_id">

                                    <div class="row g-3">
                                        <!-- Category Field -->
                                        <div class="col-md-6">
                                            <label for="edit_category" class="form-label">Category</label>
                                            <select class="form-select" id="edit_category" name="category" required>
                                                <option value="">Select Category</option>
                                                <option value="Conference">Conference</option>
                                                <option value="Workshop">Workshop</option>
                                                <option value="Seminar">Seminar</option>
                                                <option value="Training">Training</option>
                                                <option value="Webinar">Webinar</option>
                                                <option value="Networking">Networking</option>
                                                <option value="Other">Other</option>
                                            </select>
                                        </div>

                                        <!-- Title Field -->
                                        <div class="col-md-6">
                                            <label for="edit_title" class="form-label">Event Title</label>
                                            <input type="text" class="form-control" id="edit_title" name="title" required>
                                        </div>

                                        <!-- Location Field -->
                                        <div class="col-md-6">
                                            <label for="edit_location" class="form-label">Location</label>
                                            <input type="text" class="form-control" id="edit_location" name="location" required>
                                        </div>

                                        <!-- Price Field -->
                                        <div class="col-md-6">
                                            <label for="edit_price" class="form-label">Price</label>
                                            <div class="input-group">
                                                <span class="input-group-text">$</span>
                                                <input type="number" class="form-control" id="edit_price" name="price" min="0" step="0.01" required>
                                            </div>
                                        </div>

                                        <!-- Seats Field -->
                                        <div class="col-md-6">
                                            <label for="edit_seats" class="form-label">Available Seats</label>
                                            <input type="number" class="form-control" id="edit_seats" name="seats" min="1" required>
                                        </div>

                                        <!-- Event Date Field -->
                                        <div class="col-md-6">
                                            <label for="edit_event_date" class="form-label">Event Date</label>
                                            <input type="date" class="form-control" id="edit_event_date" name="event_date" required>
                                        </div>

                                        <!-- Event Starting Time Field -->
                                        <div class="col-md-3">
                                            <label for="edit_event_starting_time" class="form-label">Start Time</label>
                                            <input type="time" class="form-control" id="edit_event_starting_time" name="event_starting_time" required>
                                        </div>

                                        <!-- Event Ending Time Field -->
                                        <div class="col-md-3">
                                            <label for="edit_event_ending_time" class="form-label">End Time</label>
                                            <input type="time" class="form-control" id="edit_event_ending_time" name="event_ending_time" required>
                                        </div>

                                        <!-- Host Field -->
                                        <div class="col-md-6">
                                            <label for="edit_host" class="form-label">Host/Organizer</label>
                                            <input type="text" class="form-control" id="edit_host" name="host" required>
                                        </div>

                                        <!-- Current Image Display -->
                                        <div class="col-12">
                                            <label class="form-label">Current Image</label>
                                            <div class="mb-3">
                                                <img id="current_event_image" src="" class="img-thumbnail" style="max-width: 200px; max-height: 120px;" alt="Current Image">
                                            </div>
                                        </div>

                                        <!-- New Image Field -->
                                        <div class="col-12">
                                            <label for="edit_event_image" class="form-label">New Image (Optional)</label>
                                            <input type="file" class="form-control" id="edit_event_image" name="image" accept="image/*">
                                            <small class="text-muted">
                                                <i class="fa-solid fa-info-circle me-1"></i>
                                                Leave empty to keep current image. Supported formats: JPG, JPEG, PNG, GIF, WebP. Max size: 5MB.
                                            </small>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="submit" form="editEventForm" class="btn btn-primary">Save Changes</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Delete Event Modal -->
                <div class="modal fade" id="deleteEventModal" tabindex="-1" aria-labelledby="deleteEventModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="deleteEventModalLabel">Confirm Delete</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <p>Are you sure you want to delete <span id="deleteEventTitle" class="fw-bold"></span>? This action cannot be undone.</p>
                                <div class="alert alert-danger">
                                    <div class="d-flex align-items-center">
                                        <i class="fa-solid fa-triangle-exclamation me-2"></i>
                                        <div>
                                            <strong>Warning:</strong> The event and its associated image will be permanently deleted.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <form method="post" style="display: inline;">
                                    <input type="hidden" name="event_id" id="delete_event_id">
                                    <input type="hidden" name="delete_event" value="1">
                                    <button type="submit" class="btn btn-danger">Delete Event</button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
             <!-- Footer -->
             <div class="footer text-center bg-white shadow-sm py-3 mt-5">
                <p class="m-0">Copyright © 2025. All Rights Reserved. <a href="https://finalskills.com" class="text-primary" target="_blank" >Developed by final skills</a></p>
            </div>
        </div>
    </div>
     <!-- Scripts -->
    <script  src="./assets/js/jquery-3.6.0.min.js"></script>
    <script  src="./assets/js/bootstrap.bundle.min.js"></script>
    <script  src="./assets/plugin/chart/chart.js"></script>
    <script src="./assets/plugin/datatable/datatables.min.js"></script>
    <script  src="./assets/js/chart.js"></script>
    <script  src="./assets/js/main.js"></script>
    <script>
        $(document).ready(function() {
            // Check if table exists and has data before initializing DataTable
            if ($('#eventsTable').length && $('#eventsTable tbody tr').length > 0) {
                try {
                    // Initialize DataTable with pagination
                    const eventTable = $('#eventsTable').DataTable({
                        responsive: true,
                        order: [[7, 'asc']], // Sort by date column by default (upcoming first)
                        columnDefs: [
                            { orderable: false, targets: [1, 10] }, // Disable sorting for image and actions columns
                            { width: "5%", targets: 0 }, // ID column
                            { width: "8%", targets: 1 }, // Image column
                            { width: "10%", targets: 2 }, // Category column
                            { width: "15%", targets: 3 }, // Title column
                            { width: "12%", targets: 4 }, // Location column
                            { width: "8%", targets: 5 }, // Price column
                            { width: "8%", targets: 6 }, // Seats column
                            { width: "12%", targets: 7 }, // Date & Time column
                            { width: "10%", targets: 8 }, // Host column
                            { width: "8%", targets: 9 }, // Status column
                            { width: "12%", targets: 10 } // Actions column
                        ],
                        language: {
                            search: "_INPUT_",
                            searchPlaceholder: "Search events...",
                            lengthMenu: "Show _MENU_ events per page",
                            info: "Showing _START_ to _END_ of _TOTAL_ events",
                            infoEmpty: "No events found",
                            infoFiltered: "(filtered from _MAX_ total events)"
                        },
                        // Enable pagination
                        paging: true,
                        pagingType: "full_numbers",
                        pageLength: 10,
                        lengthMenu: [[5, 10, 25, 50, -1], [5, 10, 25, 50, "All"]]
                    });
                } catch (error) {
                    console.error('DataTable initialization error:', error);
                    // Fallback: just show the table without DataTable features
                    $('#eventsTable').show();
                }
            } else {
                // If no data, just show the table without DataTable features
                $('#eventsTable').show();
            }

            // Handle view event modal
            $(document).on('click', '.view-event', function(e) {
                e.preventDefault();

                const $this = $(this);
                const eventData = {
                    id: $this.data('id'),
                    category: $this.data('category'),
                    title: $this.data('title'),
                    location: $this.data('location'),
                    price: $this.data('price'),
                    seats: $this.data('seats'),
                    image: $this.data('image'),
                    event_date: $this.data('event-date'),
                    start_time: $this.data('start-time'),
                    end_time: $this.data('end-time'),
                    host: $this.data('host'),
                    created_at: $this.data('created'),
                    updated_at: $this.data('updated')
                };

                // Set the event image
                $('#viewEventImage').attr('src', eventData.image || './assets/images/placeholder.png');

                // Set event details
                $('#viewEventCategory').text(eventData.category || 'Not provided');
                $('#viewEventTitle').text(eventData.title || 'Not provided');
                $('#viewEventLocation').text(eventData.location || 'Not provided');
                $('#viewEventPrice').text(eventData.price > 0 ? '$' + parseFloat(eventData.price).toFixed(2) : 'Free');
                $('#viewEventSeats').text(eventData.seats || 'Not specified');
                $('#viewEventHost').text(eventData.host || 'Not provided');

                // Format the date and time
                try {
                    const eventDate = new Date(eventData.event_date).toLocaleDateString();
                    const startTime = new Date('1970-01-01T' + eventData.start_time).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                    const endTime = new Date('1970-01-01T' + eventData.end_time).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
                    $('#viewEventDateTime').html(`<i class="fas fa-calendar me-1"></i>${eventDate}<br><i class="fas fa-clock me-1"></i>${startTime} - ${endTime}`);
                } catch (error) {
                    $('#viewEventDateTime').text(`${eventData.event_date} ${eventData.start_time} - ${eventData.end_time}`);
                }

                // Format the dates
                try {
                    const createdDate = eventData.created_at ? new Date(eventData.created_at).toLocaleString() : 'Unknown';
                    const updatedDate = eventData.updated_at ? new Date(eventData.updated_at).toLocaleString() : 'Unknown';
                    $('#viewEventCreated').text(createdDate);
                    $('#viewEventUpdated').text(updatedDate);
                } catch (error) {
                    $('#viewEventCreated').text(eventData.created_at || 'Unknown');
                    $('#viewEventUpdated').text(eventData.updated_at || 'Unknown');
                }

                // Show the modal
                $('#viewEventModal').modal('show');
            });

            // Handle edit event modal
            $(document).on('click', '.edit-event', function(e) {
                e.preventDefault();

                const $this = $(this);
                const eventData = {
                    id: $this.data('id'),
                    category: $this.data('category'),
                    title: $this.data('title'),
                    location: $this.data('location'),
                    price: $this.data('price'),
                    seats: $this.data('seats'),
                    image: $this.data('image'),
                    event_date: $this.data('event-date'),
                    start_time: $this.data('start-time'),
                    end_time: $this.data('end-time'),
                    host: $this.data('host')
                };

                // Populate the edit form
                $('#edit_event_id').val(eventData.id);
                $('#edit_category').val(eventData.category);
                $('#edit_title').val(eventData.title);
                $('#edit_location').val(eventData.location);
                $('#edit_price').val(eventData.price);
                $('#edit_seats').val(eventData.seats);
                $('#edit_event_date').val(eventData.event_date);
                $('#edit_event_starting_time').val(eventData.start_time);
                $('#edit_event_ending_time').val(eventData.end_time);
                $('#edit_host').val(eventData.host);

                // Set current image
                $('#current_event_image').attr('src', eventData.image || './assets/images/placeholder.png');

                // Show the modal
                $('#editEventModal').modal('show');
            });

            // Handle delete event modal
            $(document).on('click', '.delete-event', function(e) {
                e.preventDefault();
                const eventId = $(this).data('event-id');
                const eventTitle = $(this).data('event-title');
                $('#delete_event_id').val(eventId);
                $('#deleteEventTitle').text(eventTitle);
                $('#deleteEventModal').modal('show');
            });

            // Handle edit event form submission
            $('#editEventForm').on('submit', function(e) {
                e.preventDefault();

                // Show loading indicator
                const submitBtn = $(this).find('button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="fa-solid fa-spinner fa-spin me-2"></i> Saving...');
                submitBtn.prop('disabled', true);

                const formData = new FormData(this);

                $.ajax({
                    url: 'update-event.php',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        let data;

                        try {
                            data = (typeof response === 'object') ? response : JSON.parse(response);
                        } catch (e) {
                            console.error('Error parsing JSON response:', e);
                            data = { success: false, message: 'Invalid server response' };
                        }

                        if (data.success) {
                            // Show success message before reload
                            const alertHtml = `
                                <div class="alert alert-success alert-dismissible fade show" role="alert">
                                    <i class="fa-solid fa-check-circle me-2"></i>
                                    ${data.message}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            `;

                            // Close modal
                            $('#editEventModal').modal('hide');

                            // Show message and reload after a short delay
                            $('<div class="mt-3"></div>').html(alertHtml).insertAfter('.d-flex.align-items-lg-center');
                            setTimeout(function() {
                                location.reload();
                            }, 1500);
                        } else {
                            // Show error message
                            const errorHtml = `
                                <div class="alert alert-danger mt-3 mb-0">
                                    <i class="fa-solid fa-exclamation-triangle me-2"></i>
                                    ${data.message || 'Error updating event'}
                                </div>
                            `;

                            // Replace any existing error message
                            $('#editEventForm .alert').remove();
                            $(errorHtml).prependTo('#editEventForm');

                            // Reset button
                            submitBtn.html(originalText);
                            submitBtn.prop('disabled', false);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', error);

                        // Show error message
                        const errorHtml = `
                            <div class="alert alert-danger mt-3 mb-0">
                                <i class="fa-solid fa-exclamation-triangle me-2"></i>
                                Error updating event: ${error || 'Unknown error'}
                            </div>
                        `;

                        // Replace any existing error message
                        $('#editEventForm .alert').remove();
                        $(errorHtml).prependTo('#editEventForm');

                        // Reset button
                        submitBtn.html(originalText);
                        submitBtn.prop('disabled', false);
                    }
                });
            });

            // Form validation
            (function() {
                'use strict';
                window.addEventListener('load', function() {
                    var forms = document.getElementsByClassName('needs-validation');
                    var validation = Array.prototype.filter.call(forms, function(form) {
                        form.addEventListener('submit', function(event) {
                            if (form.checkValidity() === false) {
                                event.preventDefault();
                                event.stopPropagation();
                            }
                            form.classList.add('was-validated');
                        }, false);
                    });
                }, false);
            })();
        });
    </script>
</body>
</html>